uuid: 5a5a3727-5f80-41af-9d6c-72e0359a522e
langcode: en
status: true
dependencies:
  config:
    - core.entity_form_mode.media.media_library
    - field.field.media.tv_channel_logo.field_cm_brands_hidden
    - field.field.media.tv_channel_logo.field_cm_media_cms_name
    - field.field.media.tv_channel_logo.field_media_image
    - image.style.thumbnail
    - media.type.tv_channel_logo
  module:
    - svg_image
id: media.tv_channel_logo.media_library
targetEntityType: media
bundle: tv_channel_logo
mode: media_library
content:
  field_media_image:
    type: image_image
    weight: 5
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  field_cm_brands_hidden: true
  field_cm_media_cms_name: true
  langcode: true
  path: true
  publish_on: true
  publish_state: true
  scheduler_settings: true
  status: true
  uid: true
  unpublish_on: true
  unpublish_state: true

uuid: d1834889-dcb5-47de-ac33-ac57935cf5f5
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_exclude_items
    - paragraphs.paragraphs_type.available_offers
id: paragraph.available_offers.field_exclude_items
field_name: field_exclude_items
entity_type: paragraph
bundle: available_offers
label: 'Exclude Items'
description: 'Please choose the items (products, product options or variants to exclude from the list.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: views
  handler_settings:
    view:
      view_name: cms_title_reference
      display_name: entity_reference_filter
      arguments:
        - product
        - product_option
        - product_variant
field_type: entity_reference

uuid: 5da9f51c-09cf-44b2-9f38-62b76c2b4b96
langcode: fr
status: true
dependencies:
  config:
    - field.storage.paragraph.field_media_position
    - paragraphs.paragraphs_type.teaser_text_image
  module:
    - options
id: paragraph.teaser_text_image.field_media_position
field_name: field_media_position
entity_type: paragraph
bundle: teaser_text_image
label: 'Media Position'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string

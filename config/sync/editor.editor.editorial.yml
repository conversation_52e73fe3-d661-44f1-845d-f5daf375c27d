uuid: 57bdd163-3ba8-431c-96f8-8cc2104e37df
langcode: fr
status: true
dependencies:
  config:
    - filter.format.editorial
  module:
    - ckeditor5
format: editorial
editor: ckeditor5
settings:
  toolbar:
    items:
      - bold
      - italic
      - subscript
      - superscript
      - strikethrough
      - style
      - heading
      - alignment
      - '|'
      - link
      - '|'
      - bulletedList
      - numberedList
      - '|'
      - tooltip
      - highlightbox
      - drupalMedia
      - sourceEditing
      - insertTable
      - '|'
      - blockQuote
      - code
  plugins:
    ckeditor5_alignment:
      enabled_alignments:
        - center
        - justify
        - left
        - right
    ckeditor5_heading:
      enabled_headings:
        - heading1
        - heading2
        - heading3
        - heading4
        - heading5
        - heading6
    ckeditor5_list:
      properties:
        reversed: false
        startIndex: true
      multiBlock: true
    ckeditor5_sourceEditing:
      allowed_tags:
        - '<cite>'
        - '<dl>'
        - '<dt>'
        - '<dd>'
        - '<span class title data-tooltip-uuid data-tooltip-color>'
        - '<blockquote cite>'
        - '<div id>'
        - '<ul type>'
        - '<ol type>'
        - '<h4 id>'
        - '<h5 id>'
        - '<h6 id>'
        - '<a hreflang target>'
        - '<h2 id>'
        - '<h3 id>'
    ckeditor5_style:
      styles:
        -
          label: 'H1 Center'
          element: '<h1 class="text-center">'
        -
          label: 'H2 Center'
          element: '<h2 class="text-center">'
        -
          label: 'H3 Center'
          element: '<h3 class="text-center">'
        -
          label: 'P Center'
          element: '<p class="text-center">'
        -
          label: Red
          element: '<span class="color-primary">'
        -
          label: 'Blue sky (Blick: white)'
          element: '<span class="color-secondary">'
        -
          label: Large
          element: '<span class="text-large">'
        -
          label: Small
          element: '<span class="text-small">'
        -
          label: 'List bulleted'
          element: '<ul class="list--bulleted">'
        -
          label: Highlighted
          element: '<span class="highlighted">'
        -
          label: 'P Top'
          element: '<p class="pt-4">'
        -
          label: 'P Bottom'
          element: '<p class="pb-10">'
        -
          label: 'P text-10'
          element: '<p class="text-10">'
        -
          label: 'P text-14'
          element: '<p class="text-14">'
        -
          label: 'P text-16'
          element: '<p class="text-16">'
        -
          label: 'P text-18'
          element: '<p class="text-18">'
        -
          label: 'P Text Left'
          element: '<p class="text-left">'
        -
          label: 'OL Bottom'
          element: '<ol class="pb-10">'
        -
          label: 'OL Top'
          element: '<ol class="pt-4">'
        -
          label: 'UL Bottom'
          element: '<ul class="pb-10">'
        -
          label: 'UL Top'
          element: '<ul class="pt-4">'
    editor_advanced_link_link:
      enabled_attributes:
        - class
        - id
        - target
        - title
    linkit_extension:
      linkit_enabled: true
      linkit_profile: default
    media_media:
      allow_view_mode_override: false
    wimc_ckeditor5_highlightbox:
      available_colors:
        - ''
        - yellow
        - blue
        - green
image_upload:
  status: false

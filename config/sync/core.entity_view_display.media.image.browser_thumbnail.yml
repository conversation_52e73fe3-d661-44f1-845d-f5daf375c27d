uuid: 63c274d4-4efa-454e-b41c-ffa20d4e36e2
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.browser_thumbnail
    - field.field.media.image.field_cm_brands_hidden
    - field.field.media.image.field_cm_media_cms_name
    - field.field.media.image.field_cm_tags
    - field.field.media.image.field_context
    - field.field.media.image.field_media_description
    - field.field.media.image.field_media_image
    - image.style.media_browser_thumbnail
    - media.type.image
  module:
    - svg_image
id: media.image.browser_thumbnail
targetEntityType: media
bundle: image
mode: browser_thumbnail
content:
  name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  thumbnail:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: media_browser_thumbnail
      image_loading:
        attribute: lazy
      svg_attributes:
        width: null
        height: null
      svg_render_as_image: true
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  created: true
  field_cm_brands_hidden: true
  field_cm_media_cms_name: true
  field_cm_tags: true
  field_context: true
  field_media_description: true
  field_media_image: true
  langcode: true
  search_api_excerpt: true
  uid: true

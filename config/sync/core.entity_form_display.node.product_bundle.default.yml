uuid: e1405196-91be-4fe6-a742-0a79f418a91e
langcode: fr
status: true
dependencies:
  config:
    - field.field.node.product_bundle.field_allow_content_sync
    - field.field.node.product_bundle.field_allow_nested_sync
    - field.field.node.product_bundle.field_border_color
    - field.field.node.product_bundle.field_cm_cms_title
    - field.field.node.product_bundle.field_cm_custom_id
    - field.field.node.product_bundle.field_cm_decorations
    - field.field.node.product_bundle.field_cm_decorations_mobile
    - field.field.node.product_bundle.field_cm_price_promo
    - field.field.node.product_bundle.field_cm_product_features
    - field.field.node.product_bundle.field_cm_specific_conditions
    - field.field.node.product_bundle.field_cta
    - field.field.node.product_bundle.field_cta_secondary
    - field.field.node.product_bundle.field_last_chance
    - field.field.node.product_bundle.field_last_chance_override
    - field.field.node.product_bundle.field_marketing_sentence
    - field.field.node.product_bundle.field_option
    - field.field.node.product_bundle.field_premium_features
    - field.field.node.product_bundle.field_product
    - field.field.node.product_bundle.field_promo_color
    - field.field.node.product_bundle.field_title
    - field.field.node.product_bundle.field_usp
    - node.type.product_bundle
    - workflows.workflow.content_scheduler
  module:
    - content_moderation
    - field_group
    - media_library
    - paragraphs
    - path
    - scheduler
    - scheduler_content_moderation_integration
    - text
third_party_settings:
  field_group:
    group_title:
      children:
        - langcode
        - field_cm_cms_title
        - title
        - field_cm_custom_id
        - field_title
      label: 'Basic Info'
      region: content
      parent_name: group_content
      weight: 11
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: false
        description: ''
        required_fields: true
    group_content_references:
      children:
        - field_product
        - field_option
      label: References
      region: content
      parent_name: group_content
      weight: 12
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_features:
      children:
        - field_premium_features
        - field_cm_product_features
      label: Features
      region: content
      parent_name: group_content
      weight: 14
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_cta:
      children:
        - group_primary
        - group_secondary
      label: CTA
      region: content
      parent_name: group_content
      weight: 15
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_specific_conditions:
      children:
        - field_cm_specific_conditions
      label: 'Specific Conditions'
      region: content
      parent_name: group_content
      weight: 18
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_container:
      children:
        - group_content
        - group_media
        - group_synchronization
        - group_validity_dates
      label: Container
      region: content
      parent_name: ''
      weight: 0
      format_type: tabs
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        direction: horizontal
        width_breakpoint: 640
    group_content:
      children:
        - group_title
        - group_content_references
        - group_price
        - group_features
        - group_cta
        - group_product_configurator
        - group_specific_conditions
        - group_colors
      label: Content
      region: content
      parent_name: group_container
      weight: 15
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_synchronization:
      children:
        - field_allow_content_sync
        - field_allow_nested_sync
      label: Synchronization
      region: content
      parent_name: group_container
      weight: 17
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_validity_dates:
      children:
        - group_scheduler_dates
        - group_variant_dates
        - group_last_chance_mode_control
      label: 'Scheduling Options / Validity'
      region: content
      parent_name: group_container
      weight: 18
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_scheduler_dates:
      children:
        - publish_on
        - publish_state
        - unpublish_on
        - unpublish_state
      label: 'Scheduler Dates'
      region: content
      parent_name: group_validity_dates
      weight: 4
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_variant_dates:
      children:
        - start_date
        - end_date
      label: 'Variant Validity Dates'
      region: content
      parent_name: group_validity_dates
      weight: 5
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_price:
      children:
        - field_cm_price_promo
      label: Promo
      region: content
      parent_name: group_content
      weight: 13
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_product_configurator:
      children:
        - field_usp
        - field_marketing_sentence
      label: 'Product Configurator'
      region: content
      parent_name: group_content
      weight: 17
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_primary:
      children:
        - field_cta
      label: Primary
      region: content
      parent_name: group_cta
      weight: 20
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_secondary:
      children:
        - field_cta_secondary
      label: Secondary
      region: content
      parent_name: group_cta
      weight: 22
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_media:
      children:
        - field_cm_decorations
        - field_cm_decorations_mobile
      label: Media
      region: content
      parent_name: group_container
      weight: 16
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_colors:
      children:
        - field_promo_color
        - field_border_color
      label: Colors
      region: hidden
      parent_name: group_content
      weight: 14
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_last_chance_mode_control:
      children:
        - field_last_chance
        - field_last_chance_override
      label: 'Last Chance Mode Control'
      region: hidden
      parent_name: group_validity_dates
      weight: 6
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
id: node.product_bundle.default
targetEntityType: node
bundle: product_bundle
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  end_date:
    type: datetime_timestamp_no_default
    weight: 18
    region: content
    settings: {  }
    third_party_settings: {  }
  field_allow_content_sync:
    type: boolean_checkbox
    weight: 30
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_allow_nested_sync:
    type: boolean_checkbox
    weight: 31
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_border_color:
    type: options_select
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_cms_title:
    type: string_textfield
    weight: 13
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_custom_id:
    type: string_textfield
    weight: 15
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_decorations:
    type: media_library_widget
    weight: 4
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_cm_decorations_mobile:
    type: media_library_widget
    weight: 6
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_cm_price_promo:
    type: number
    weight: 4
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cm_product_features:
    type: paragraphs
    weight: 28
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: product_feature
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_cm_specific_conditions:
    type: text_textarea
    weight: 5
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_cta:
    type: paragraphs
    weight: 17
    region: content
    settings:
      title: Paragraphe
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_cta_secondary:
    type: paragraphs
    weight: 18
    region: content
    settings:
      title: Paragraphe
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_last_chance:
    type: boolean_checkbox
    weight: 12
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_last_chance_override:
    type: boolean_checkbox
    weight: 14
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_marketing_sentence:
    type: text_textarea
    weight: 7
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_option:
    type: entity_reference_autocomplete
    weight: 3
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_premium_features:
    type: entity_reference_autocomplete_tags
    weight: 27
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_product:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_promo_color:
    type: options_select
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_title:
    type: text_textfield
    weight: 16
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_usp:
    type: options_select
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 12
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: 14
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_state:
    type: scheduler_moderation
    weight: 15
    region: content
    settings: {  }
    third_party_settings: {  }
  scheduler_settings:
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  start_date:
    type: datetime_timestamp_no_default
    weight: 17
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 10
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 14
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: 17
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_state:
    type: scheduler_moderation
    weight: 18
    region: content
    settings: {  }
    third_party_settings: {  }
  url_redirects:
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  cm_meta_description: true
  cm_meta_title: true

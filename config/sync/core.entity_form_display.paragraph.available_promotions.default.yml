uuid: 75b507bd-09a7-4b30-9eb0-a9bfba2b9ce0
langcode: de
status: true
dependencies:
  config:
    - field.field.paragraph.available_promotions.field_call_to_action_variants
    - field.field.paragraph.available_promotions.field_cm_headlines
    - field.field.paragraph.available_promotions.field_cm_number_of_items
    - field.field.paragraph.available_promotions.field_cm_products
    - field.field.paragraph.available_promotions.field_cm_render_component
    - field.field.paragraph.available_promotions.field_cm_subtitle
    - field.field.paragraph.available_promotions.field_default_product
    - field.field.paragraph.available_promotions.field_display_breadcrumb
    - field.field.paragraph.available_promotions.field_display_hotline_number
    - field.field.paragraph.available_promotions.field_display_usp
    - field.field.paragraph.available_promotions.field_exclude_variants
    - field.field.paragraph.available_promotions.field_fallback_to_product
    - field.field.paragraph.available_promotions.field_hide_bottom_sheet
    - field.field.paragraph.available_promotions.field_hide_market_sentence_mob
    - field.field.paragraph.available_promotions.field_hotline
    - field.field.paragraph.available_promotions.field_hotline_location
    - field.field.paragraph.available_promotions.field_hotline_render
    - field.field.paragraph.available_promotions.field_hotline_type
    - paragraphs.paragraphs_type.available_promotions
  module:
    - field_group
    - paragraphs
third_party_settings:
  field_group:
    group_products:
      children:
        - field_default_product
        - field_cm_products
        - field_exclude_variants
        - field_fallback_to_product
        - field_cm_number_of_items
      label: References
      region: content
      parent_name: ''
      weight: 2
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_general_info:
      children:
        - field_cm_headlines
        - title
        - field_cm_subtitle
        - wimc_cm_id
      label: 'General Info'
      region: content
      parent_name: ''
      weight: 1
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_display:
      children:
        - field_cm_render_component
        - field_display_breadcrumb
        - field_display_usp
        - field_hide_bottom_sheet
        - field_hide_market_sentence_mob
        - group_hotline
      label: Config
      region: content
      parent_name: ''
      weight: 3
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_hotline:
      children:
        - group_hotline_manual
        - group_hotline_reference
      label: Hotline
      region: content
      parent_name: group_display
      weight: 16
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_cta:
      children:
        - field_call_to_action_variants
      label: CTA
      region: content
      parent_name: ''
      weight: 4
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_hotline_manual:
      children:
        - field_display_hotline_number
        - field_hotline_type
        - field_hotline_render
        - field_hotline_location
      label: 'Hotline (DEPRECATED)'
      region: content
      parent_name: group_hotline
      weight: 20
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_hotline_reference:
      children:
        - field_hotline
      label: 'Hotline Reference'
      region: content
      parent_name: group_hotline
      weight: 21
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
id: paragraph.available_promotions.default
targetEntityType: paragraph
bundle: available_promotions
mode: default
content:
  brands_hidden:
    type: options_buttons
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_call_to_action_variants:
    type: options_select
    weight: 19
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_headlines:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_number_of_items:
    type: number
    weight: 9
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cm_products:
    type: entity_reference_autocomplete
    weight: 6
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_render_component:
    type: options_select
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_subtitle:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_default_product:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_display_breadcrumb:
    type: boolean_checkbox
    weight: 12
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_display_hotline_number:
    type: boolean_checkbox
    weight: 21
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_display_usp:
    type: boolean_checkbox
    weight: 13
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_exclude_variants:
    type: entity_reference_autocomplete
    weight: 7
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_fallback_to_product:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_hide_bottom_sheet:
    type: boolean_checkbox
    weight: 14
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_hide_market_sentence_mob:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_hotline:
    type: paragraphs
    weight: 22
    region: content
    settings:
      title: Paragraphe
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_hotline_location:
    type: options_select
    weight: 24
    region: content
    settings: {  }
    third_party_settings: {  }
  field_hotline_render:
    type: options_select
    weight: 23
    region: content
    settings: {  }
    third_party_settings: {  }
  field_hotline_type:
    type: options_select
    weight: 22
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  color_pattern: true
  compact: true
  created: true

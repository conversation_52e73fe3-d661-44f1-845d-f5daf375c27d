uuid: 14ac9214-3a27-44e5-910d-1694ccad97fa
langcode: fr
status: true
dependencies:
  config:
    - field.field.paragraph.product_configurator.field_cm_cta
    - field.field.paragraph.product_configurator.field_cm_related_bg_color
    - field.field.paragraph.product_configurator.field_content_type
    - paragraphs.paragraphs_type.product_configurator
  module:
    - field_group
    - paragraphs
third_party_settings:
  field_group:
    group_config:
      children:
        - field_content_type
        - field_cm_related_bg_color
        - status
      label: Config
      region: content
      parent_name: ''
      weight: 3
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
id: paragraph.product_configurator.default
targetEntityType: paragraph
bundle: product_configurator
mode: default
content:
  field_cm_cta:
    type: paragraphs
    weight: 1
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_cm_related_bg_color:
    type: options_select
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  field_content_type:
    type: options_select
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  brands_hidden: true
  color_pattern: true
  compact: true
  created: true

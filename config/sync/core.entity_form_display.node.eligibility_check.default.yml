uuid: 6b7acc72-305b-4110-b120-4cecc0d0c6e2
langcode: fr
status: true
dependencies:
  config:
    - field.field.node.eligibility_check.field_allow_content_sync
    - field.field.node.eligibility_check.field_allow_nested_sync
    - field.field.node.eligibility_check.field_breadcrumb_title
    - field.field.node.eligibility_check.field_clp_redir_info_message
    - field.field.node.eligibility_check.field_clp_reference
    - field.field.node.eligibility_check.field_cm_media
    - field.field.node.eligibility_check.field_cm_media_mobile
    - field.field.node.eligibility_check.field_cm_metatags
    - field.field.node.eligibility_check.field_cm_text
    - field.field.node.eligibility_check.field_eligibility_check_message
    - field.field.node.eligibility_check.field_hide_email_input
    - field.field.node.eligibility_check.field_hide_password_input
    - node.type.eligibility_check
  module:
    - content_moderation
    - field_group
    - media_library
    - metatag
    - path
    - scheduler
    - text
third_party_settings:
  field_group:
    group_configuration:
      children:
        - langcode
        - title
        - field_hide_password_input
        - field_hide_email_input
        - field_clp_reference
      label: Configuration
      region: content
      parent_name: group_wrapper
      weight: 33
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
        open: false
    group_media:
      children:
        - field_cm_media
        - field_cm_media_mobile
      label: Media
      region: content
      parent_name: group_wrapper
      weight: 35
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
        open: false
    group_message:
      children:
        - field_cm_text
        - field_clp_redir_info_message
        - field_eligibility_check_message
      label: Message
      region: content
      parent_name: group_wrapper
      weight: 34
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
        open: false
    group_synchronization:
      children:
        - field_allow_content_sync
        - field_allow_nested_sync
      label: Synchronization
      region: content
      parent_name: group_wrapper
      weight: 37
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
        open: false
    group_wrapper:
      children:
        - group_configuration
        - group_message
        - group_media
        - group_seo
        - group_synchronization
      label: Wrapper
      region: content
      parent_name: ''
      weight: 0
      format_type: tabs
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        direction: horizontal
        width_breakpoint: 640
    group_seo:
      children:
        - cm_meta_title
        - field_breadcrumb_title
      label: SEO
      region: content
      parent_name: group_wrapper
      weight: 36
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: false
        description: ''
        required_fields: true
id: node.eligibility_check.default
targetEntityType: node
bundle: eligibility_check
mode: default
content:
  cm_meta_title:
    type: string_textarea
    weight: 0
    region: content
    settings:
      rows: 1
      placeholder: ''
    third_party_settings:
      advanced_text_formatter:
        show_token_tree: 0
  created:
    type: datetime_timestamp
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  end_date:
    type: datetime_timestamp_no_default
    weight: 31
    region: content
    settings: {  }
    third_party_settings: {  }
  field_allow_content_sync:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_allow_nested_sync:
    type: boolean_checkbox
    weight: 17
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_breadcrumb_title:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_clp_redir_info_message:
    type: text_textarea
    weight: 15
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_clp_reference:
    type: options_select
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_media:
    type: media_library_widget
    weight: 14
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_cm_media_mobile:
    type: media_library_widget
    weight: 15
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_cm_metatags:
    type: metatag_firehose
    weight: 13
    region: content
    settings:
      sidebar: true
      use_details: true
    third_party_settings: {  }
  field_cm_text:
    type: text_textarea
    weight: 14
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_eligibility_check_message:
    type: text_textarea
    weight: 16
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_hide_email_input:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_hide_password_input:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  start_date:
    type: datetime_timestamp_no_default
    weight: 31
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 12
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 4
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  cm_meta_description: true
  publish_on: true
  publish_state: true
  unpublish_on: true
  unpublish_state: true

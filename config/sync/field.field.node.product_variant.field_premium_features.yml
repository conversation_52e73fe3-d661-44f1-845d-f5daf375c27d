uuid: 39e4537d-423a-44f6-bef3-528bc4d3fba5
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_premium_features
    - node.type.product_variant
    - taxonomy.vocabulary.product_premium_features
id: node.product_variant.field_premium_features
field_name: field_premium_features
entity_type: node
bundle: product_variant
label: 'Premium Features'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      product_premium_features: product_premium_features
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference

uuid: 614202da-05d9-4885-bdec-0eccf0659864
langcode: fr
status: true
dependencies:
  config:
    - media.type.image
id: media.image.uid
field_name: uid
entity_type: media
bundle: image
label: 'Écrit par'
description: "L'identifiant (ID) de l'auteur."
required: false
translatable: true
default_value: {  }
default_value_callback: 'Drupal\media\Entity\Media::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference

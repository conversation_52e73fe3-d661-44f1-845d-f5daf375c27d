uuid: a3f2149f-4951-4b85-b895-6caee0ec1325
langcode: de
status: true
dependencies:
  config:
    - field.storage.node.field_cm_analytics_p_rec_price
    - node.type.customer_landing_page
id: node.customer_landing_page.field_cm_analytics_p_rec_price
field_name: field_cm_analytics_p_rec_price
entity_type: node
bundle: customer_landing_page
label: 'Product recurring price'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  min: ''
  max: ''
  prefix: ''
  suffix: ''
field_type: string

uuid: 9dbfa269-878e-4954-816d-f2378c417593
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.quick_link.field_cm_link
    - field.field.paragraph.quick_link.field_item_type
    - field.field.paragraph.quick_link.field_quick_link_id
    - field.field.paragraph.quick_link.field_quick_link_media
    - field.field.paragraph.quick_link.field_quick_link_media_hover
    - field.field.paragraph.quick_link.field_title
    - paragraphs.paragraphs_type.quick_link
  module:
    - field_group
    - link
    - media_library
    - text
third_party_settings:
  field_group:
    group_content:
      children:
        - group_config
        - group_asset
        - group_config2
      label: Content
      region: content
      parent_name: ''
      weight: 0
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_asset:
      children:
        - field_quick_link_media
        - field_quick_link_media_hover
      label: Asset
      region: content
      parent_name: group_content
      weight: 5
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_config:
      children:
        - field_title
        - field_quick_link_id
        - field_item_type
        - field_cm_link
      label: 'General Info'
      region: content
      parent_name: group_content
      weight: 4
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_config2:
      children:
        - status
      label: Config
      region: content
      parent_name: group_content
      weight: 6
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
id: paragraph.quick_link.default
targetEntityType: paragraph
bundle: quick_link
mode: default
content:
  field_cm_link:
    type: link_default
    weight: 6
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_item_type:
    type: options_select
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_quick_link_id:
    type: options_select
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_quick_link_media:
    type: media_library_widget
    weight: 5
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_quick_link_media_hover:
    type: media_library_widget
    weight: 6
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_title:
    type: text_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  translation:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  brands_hidden: true
  color_pattern: true
  compact: true
  created: true
  title: true
  wimc_cm_id: true

uuid: 3413b6cd-b0bb-4ca7-8fbc-46ec7ba5cf24
langcode: de
status: true
dependencies:
  config:
    - field.field.paragraph.subscription_details.field_cm_product_variant
    - field.field.paragraph.subscription_details.field_cm_related_bg_color
    - field.field.paragraph.subscription_details.field_subtitle
    - paragraphs.paragraphs_type.subscription_details
  module:
    - field_group
    - text
    - textfield_counter
third_party_settings:
  field_group:
    group_config:
      children:
        - field_cm_related_bg_color
        - status
      label: Config
      region: content
      parent_name: ''
      weight: 5
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
id: paragraph.subscription_details.default
targetEntityType: paragraph
bundle: subscription_details
mode: default
content:
  brands_hidden:
    type: options_buttons
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_product_variant:
    type: entity_reference_autocomplete
    weight: 3
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_related_bg_color:
    type: options_select
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_subtitle:
    type: text_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield_with_counter
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
      use_field_maxlength: false
      maxlength: 0
      counter_position: after
      js_prevent_submit: true
      count_only_mode: false
      count_html_characters: true
      textcount_status_message: 'Maxlength: <span class="maxlength_count">@maxlength</span><br />Used: <span class="current_count">@current_length</span><br />Remaining: <span class="remaining_count">@remaining_count</span>'
    third_party_settings: {  }
hidden:
  color_pattern: true
  compact: true
  created: true
  translation: true

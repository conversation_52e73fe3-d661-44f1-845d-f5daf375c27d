uuid: 52c2ceb6-f3ff-4580-a99a-b5884f5fd9c7
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_cm_specific_conditions
    - node.type.product
  module:
    - text
id: node.product.field_cm_specific_conditions
field_name: field_cm_specific_conditions
entity_type: node
bundle: product
label: 'Specific Conditions'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: text_long

uuid: fe0eec73-0561-49ab-a9e9-172a162ebce4
langcode: en
status: true
dependencies:
  config:
    - media.type.tv_channel_logo
id: media.tv_channel_logo.uid
field_name: uid
entity_type: media
bundle: tv_channel_logo
label: 'Authored by'
description: 'The user ID of the author.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\media\Entity\Media::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference

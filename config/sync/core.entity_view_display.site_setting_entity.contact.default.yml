uuid: f2a5c9e4-6e95-4334-99e4-6ac13142571b
langcode: fr
status: true
dependencies:
  config:
    - field.field.site_setting_entity.contact.field_items
    - site_settings.site_setting_entity_type.contact
  module:
    - entity_reference_revisions
    - user
id: site_setting_entity.contact.default
targetEntityType: site_setting_entity
bundle: contact
mode: default
content:
  description:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_items:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 2
    region: content
  group:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -4
    region: content
  user_id:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  langcode: true
  search_api_excerpt: true

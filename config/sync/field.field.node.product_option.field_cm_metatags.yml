uuid: 4c8691ab-fa42-4759-b30b-35a6c987f593
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_cm_metatags
    - node.type.product_option
  module:
    - metatag
id: node.product_option.field_cm_metatags
field_name: field_cm_metatags
entity_type: node
bundle: product_option
label: 'Meta tags'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: metatag

uuid: 4f23cd42-2230-42a1-9198-859dd65b058a
langcode: de
status: true
dependencies:
  config:
    - field.storage.node.field_last_chance
    - node.type.product_bundle
id: node.product_bundle.field_last_chance
field_name: field_last_chance
entity_type: node
bundle: product_bundle
label: 'Last Chance'
description: 'Field to enable last chance mode. This field is automatically set by cron job based on the related business rule. Any manual modification will be lost in the next cron run.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  on_label: 'On'
  off_label: 'Off'
field_type: boolean

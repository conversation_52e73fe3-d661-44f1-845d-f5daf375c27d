uuid: 043326a2-9eba-4932-9a4c-d6b0137e5194
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_clp_reference
    - node.type.eligibility_check
id: node.eligibility_check.field_clp_reference
field_name: field_clp_reference
entity_type: node
bundle: eligibility_check
label: 'CLP reference'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: views
  handler_settings:
    view:
      view_name: cms_title_reference
      display_name: entity_reference_filter
      arguments:
        - customer_landing_page
field_type: entity_reference

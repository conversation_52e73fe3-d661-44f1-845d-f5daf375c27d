uuid: b3af9d1e-1541-4924-9a0b-4992a154c7f1
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.roaming.field_cm_id
    - field.field.paragraph.roaming.field_cm_media
    - field.field.paragraph.roaming.field_cm_products
    - field.field.paragraph.roaming.field_cm_text
    - field.field.paragraph.roaming.field_roaming_country
    - field.field.paragraph.roaming.field_roaming_default_product
    - paragraphs.paragraphs_type.roaming
  module:
    - media_library
    - text
id: paragraph.roaming.default
targetEntityType: paragraph
bundle: roaming
mode: default
content:
  brands_hidden:
    type: options_buttons
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  decoration_pattern:
    type: options_select
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_media:
    type: media_library_widget
    weight: 3
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_cm_products:
    type: entity_reference_autocomplete
    weight: 4
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 20
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cm_text:
    type: text_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_roaming_country:
    type: entity_reference_autocomplete
    weight: 8
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 20
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_roaming_default_product:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 20
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 10
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      advanced_text_formatter:
        show_token_tree: '1'
  translation:
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 9
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  color_pattern: true
  compact: true
  created: true
  field_cm_id: true
  uid: true

uuid: 740139dd-a58e-43e7-a8ab-46249da258af
langcode: fr
status: true
dependencies:
  config:
    - field.storage.paragraph.field_offer_references
    - node.type.product
    - node.type.product_bundle
    - node.type.product_option
    - node.type.product_variant
    - paragraphs.paragraphs_type.dynamic_hero
id: paragraph.dynamic_hero.field_offer_references
field_name: field_offer_references
entity_type: paragraph
bundle: dynamic_hero
label: References
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      product_variant: product_variant
      product: product
      product_bundle: product_bundle
      product_option: product_option
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: product
field_type: entity_reference

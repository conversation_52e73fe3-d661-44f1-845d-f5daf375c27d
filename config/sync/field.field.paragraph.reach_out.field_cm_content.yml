uuid: eb0a1a6b-1845-4665-bd26-f9b604c28c01
langcode: fr
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_content
    - paragraphs.paragraphs_type.reach_out
    - paragraphs.paragraphs_type.title_text_element
  module:
    - entity_reference_revisions
id: paragraph.reach_out.field_cm_content
field_name: field_cm_content
entity_type: paragraph
bundle: reach_out
label: 'Title Text Element'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      title_text_element: title_text_element
    negate: 0
    target_bundles_drag_drop:
      available_products:
        weight: 83
        enabled: false
      available_promotions:
        weight: 84
        enabled: false
      chatbot:
        weight: 86
        enabled: false
      consent_vendors:
        weight: 57
        enabled: false
      container:
        weight: 88
        enabled: false
      content_tab:
        weight: 89
        enabled: false
      content_tabs:
        weight: 90
        enabled: false
      content_teaser:
        weight: 91
        enabled: false
      cta:
        weight: 96
        enabled: false
      faqs:
        weight: 100
        enabled: false
      from_library:
        weight: 104
        enabled: false
      help:
        weight: 105
        enabled: false
      help_articles:
        weight: 66
        enabled: false
      horizontal_scroll:
        weight: 67
        enabled: false
      horizontal_scroll_tag:
        weight: 68
        enabled: false
      horizontal_story:
        weight: 109
        enabled: false
      hotline_sales:
        weight: 70
        enabled: false
      iframe:
        weight: 113
        enabled: false
      intro_product:
        weight: 114
        enabled: false
      invoice_explanation:
        weight: 115
        enabled: false
      invoice_explanation_item:
        weight: 116
        enabled: false
      media:
        weight: 117
        enabled: false
      medias:
        weight: 118
        enabled: false
      multimedia_element:
        weight: 119
        enabled: false
      news:
        weight: 120
        enabled: false
      product_feature:
        weight: 121
        enabled: false
      product_option:
        weight: 122
        enabled: false
      product_options:
        weight: 81
        enabled: false
      product_scale:
        weight: 123
        enabled: false
      product_scale_option:
        weight: 124
        enabled: false
      product_tab:
        weight: 125
        enabled: false
      product_tabs:
        weight: 126
        enabled: false
      radio_channels:
        weight: 128
        enabled: false
      reach_out:
        weight: 129
        enabled: false
      related_help_articles:
        weight: 88
        enabled: false
      roaming:
        weight: 131
        enabled: false
      search:
        weight: 132
        enabled: false
      search_form:
        weight: 133
        enabled: false
      smartphones:
        weight: 134
        enabled: false
      spacer:
        weight: 135
        enabled: false
      stage_main:
        weight: 136
        enabled: false
      stage_simple:
        weight: 137
        enabled: false
      story_multimedia:
        weight: 141
        enabled: false
      subscription_detail_item:
        weight: 143
        enabled: false
      subscription_details:
        weight: 142
        enabled: false
      teaser_text_image:
        weight: 155
        enabled: false
      text:
        weight: 156
        enabled: false
      text_image:
        weight: 157
        enabled: false
      title_text_element:
        weight: 158
        enabled: true
      tv_channels:
        weight: 160
        enabled: false
      usp_section:
        weight: 161
        enabled: false
      usp_section_item:
        weight: 162
        enabled: false
      webform:
        weight: 163
        enabled: false
field_type: entity_reference_revisions

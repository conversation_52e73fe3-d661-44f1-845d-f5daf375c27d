uuid: 8d14de00-508f-4655-a183-86826eda9d25
langcode: fr
status: true
dependencies:
  config:
    - field.field.paragraph.reach_out.field_cm_content
    - field.field.paragraph.reach_out.field_cm_media
    - field.field.paragraph.reach_out.field_cm_related_bg_color
    - field.field.paragraph.reach_out.field_tagline
    - paragraphs.paragraphs_type.reach_out
  module:
    - media_library
    - paragraphs
id: paragraph.reach_out.default
targetEntityType: paragraph
bundle: reach_out
mode: default
content:
  brands_hidden:
    type: options_buttons
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_content:
    type: paragraphs
    weight: 5
    region: content
    settings:
      title: Paragraphe
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: title_text_element
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_cm_media:
    type: media_library_widget
    weight: 4
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_cm_related_bg_color:
    type: options_select
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_tagline:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 6
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  color_pattern: true
  compact: true
  created: true

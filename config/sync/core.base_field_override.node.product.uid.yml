uuid: fa49e02d-1f4e-4b51-bba4-5786faf5e6bb
langcode: fr
status: true
dependencies:
  config:
    - node.type.product
id: node.product.uid
field_name: uid
entity_type: node
bundle: product
label: 'Écrit par'
description: "Le nom d'utilisateur de l'auteur du contenu."
required: false
translatable: true
default_value: {  }
default_value_callback: 'Drupal\node\Entity\Node::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference

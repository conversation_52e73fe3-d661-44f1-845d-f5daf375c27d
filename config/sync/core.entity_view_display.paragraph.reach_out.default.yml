uuid: d309d5fb-38dc-4af6-ba4a-e2ffc61349e8
langcode: fr
status: true
dependencies:
  config:
    - field.field.paragraph.reach_out.field_cm_content
    - field.field.paragraph.reach_out.field_cm_media
    - field.field.paragraph.reach_out.field_cm_related_bg_color
    - field.field.paragraph.reach_out.field_tagline
    - paragraphs.paragraphs_type.reach_out
  module:
    - entity_reference_revisions
id: paragraph.reach_out.default
targetEntityType: paragraph
bundle: reach_out
mode: default
content:
  field_cm_content:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 3
    region: content
  field_cm_media:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 2
    region: content
  field_cm_related_bg_color:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 1
    region: content
  field_tagline:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  search_api_excerpt: true

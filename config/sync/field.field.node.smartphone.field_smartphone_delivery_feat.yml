uuid: d7d6a08f-5345-4bd6-9903-45919308225f
langcode: de
status: true
dependencies:
  config:
    - field.storage.node.field_smartphone_delivery_feat
    - node.type.smartphone
  module:
    - text
_core:
  default_config_hash: osygBJrpIxuMpknkLetlx5yYvsXd1RqiFUq12d8E0MQ
id: node.smartphone.field_smartphone_delivery_feat
field_name: field_smartphone_delivery_feat
entity_type: node
bundle: smartphone
label: 'Delivery Features'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: text_long

uuid: 188a7282-b652-47d5-8e6e-9fa396503228
langcode: de
status: true
dependencies:
  config:
    - paragraphs.paragraphs_type.reach_out
    - paragraphs.paragraphs_type.teaser_text_image
    - paragraphs.paragraphs_type.text
  module:
    - entity_browser
    - paragraphs
    - paragraphs_library
_core:
  default_config_hash: PZWQQUZ900oUouSDZFDokKyDnpjh2REA1CCBU_tllsY
id: paragraphs_library_browser
label: 'Paragraphs library browser'
module: views
description: ''
tag: ''
base_table: paragraphs_library_item_field_data
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'Paragraphs library'
      fields:
        entity_browser_select:
          id: entity_browser_select
          table: paragraphs_library_item
          field: entity_browser_select
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          plugin_id: entity_browser_select
          label: Select
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        label:
          id: label
          table: paragraphs_library_item_field_data
          field: label
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: label
          plugin_id: field
          label: Label
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        type:
          id: type
          table: paragraphs_item_field_data
          field: type
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          entity_type: paragraph
          entity_field: type
          plugin_id: field
          label: Type
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        paragraphs__target_id:
          id: paragraphs__target_id
          table: paragraphs_library_item_field_data
          field: paragraphs__target_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: paragraphs
          plugin_id: field
          label: Paragraphs
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: paragraph_summary
          settings: {  }
          group_column: entity_id
          group_columns:
            target_id: target_id
            target_revision_id: target_revision_id
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        changed:
          id: changed
          table: paragraphs_library_item_field_data
          field: changed
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: changed
          plugin_id: field
          label: Changed
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 50
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Elemente pro Seite'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- Alle -'
            offset: false
            offset_label: Versatz
      exposed_form:
        type: basic
        options:
          submit_button: Filter
          reset_button: false
          reset_button_label: Zurücksetzen
          exposed_sorts_label: 'Sortieren nach'
          expose_sort_order: true
          sort_asc_label: Aufsteigend
          sort_desc_label: Absteigend
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'No library items available.'
          tokenize: false
      sorts: {  }
      arguments: {  }
      filters:
        label:
          id: label
          table: paragraphs_library_item_field_data
          field: label
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: label
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: label_op
            label: Label
            description: ''
            use_operator: false
            operator: label_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: label
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: Label
            description: null
            identifier: label
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1: {  }
              2: {  }
              3: {  }
        type:
          id: type
          table: paragraphs_item_field_data
          field: type
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          entity_type: paragraph
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            reach_out: reach_out
            teaser_text_image: teaser_text_image
            text: text
          group: 1
          exposed: true
          expose:
            operator_id: type_op
            label: Type
            description: ''
            use_operator: false
            operator: type_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: type
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              webmaster: '0'
              external: '0'
            reduce: true
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        default_langcode:
          id: default_langcode
          table: paragraphs_library_item_field_data
          field: default_langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: default_langcode
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        default_langcode_1:
          id: default_langcode_1
          table: paragraphs_item_field_data
          field: default_langcode
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          entity_type: paragraph
          entity_field: default_langcode
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            label: label
            type: type
            id: id
            count: count
            operations: operations
          default: '-1'
          info:
            label:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            type:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            id:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            count:
              sortable: false
              default_sort_order: asc
              align: views-align-right
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: true
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        paragraphs__target_id:
          id: paragraphs__target_id
          table: paragraphs_library_item_field_data
          field: paragraphs__target_id
          relationship: none
          group_type: group
          admin_label: Paragraphs
          entity_type: paragraphs_library_item
          entity_field: paragraphs
          plugin_id: standard
          required: true
      use_ajax: true
      group_by: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
      tags: {  }
  entity_browser:
    id: entity_browser
    display_title: 'Entity browser'
    display_plugin: entity_browser
    position: 2
    display_options:
      rendering_language: '***LANGUAGE_language_interface***'
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
      tags: {  }

uuid: 1646d3a2-2620-4b06-80ac-8e2f113a36f0
langcode: fr
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_label
    - paragraphs.paragraphs_type.product_option
id: paragraph.product_option.field_cm_label
field_name: field_cm_label
entity_type: paragraph
bundle: product_option
label: 'Price Label'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string

uuid: e34ec283-8861-4f80-8250-da70f0f18963
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_cm_analytics_p_rec_price
    - node.type.page
id: node.page.field_cm_analytics_p_rec_price
field_name: field_cm_analytics_p_rec_price
entity_type: node
bundle: page
label: 'Product recurring price'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  min: 0.0
  max: null
  prefix: ''
  suffix: ''
field_type: float

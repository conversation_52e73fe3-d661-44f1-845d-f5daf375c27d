uuid: d1f39044-cc76-4401-851b-8230ba915e5b
langcode: de
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_reference
    - paragraphs.paragraphs_type.intro_product
id: paragraph.intro_product.field_cm_reference
field_name: field_cm_reference
entity_type: paragraph
bundle: intro_product
label: 'Product or Variant'
description: "Choose a product or a variant. If a product is select the next active variant of the product will be displayed. If a variant is selected, then display it until it's no longer published."
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: views
  handler_settings:
    view:
      view_name: cms_title_reference
      display_name: entity_reference_filter
      arguments:
        - product_variant
        - product
        - product_option
field_type: entity_reference

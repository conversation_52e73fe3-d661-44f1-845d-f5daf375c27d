uuid: d48d35a3-0bac-4ba9-98e5-ef504cafd82b
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.invoice_explanation_item.field_cm_text
    - paragraphs.paragraphs_type.invoice_explanation_item
  module:
    - advanced_text_formatter
    - text
    - wimc_paragraphs
id: paragraph.invoice_explanation_item.default
targetEntityType: paragraph
bundle: invoice_explanation_item
mode: default
content:
  brands_hidden:
    type: options_buttons
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cm_text:
    type: text_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings:
      advanced_text_formatter:
        show_token_tree: '1'
  title:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      advanced_text_formatter:
        show_token_tree: '1'
  translation:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  color_pattern: true
  compact: true
  created: true
  status: true
  uid: true

uuid: 421292c1-37b0-4ba9-bd7a-61d77b4ecb49
langcode: fr
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_text
    - paragraphs.paragraphs_type.subscription_detail_item
  module:
    - text
id: paragraph.subscription_detail_item.field_cm_text
field_name: field_cm_text
entity_type: paragraph
bundle: subscription_detail_item
label: Text
description: 'Feature text at one mobile region. Example: for the feature <i>SMS</i>, it could be <b>"Unlimited"</b>'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: text_long

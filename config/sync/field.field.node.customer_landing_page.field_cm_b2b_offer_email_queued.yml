uuid: 5fa5af7c-208a-4ad1-b88d-044748ec21f0
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_cm_b2b_offer_email_queued
    - node.type.customer_landing_page
  module:
    - text
id: node.customer_landing_page.field_cm_b2b_offer_email_queued
field_name: field_cm_b2b_offer_email_queued
entity_type: node
bundle: customer_landing_page
label: Processed
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: text_long

uuid: 32e88f33-cb3c-4580-8710-b5812e1759ee
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_render_component
    - paragraphs.paragraphs_type.available_promotions
  module:
    - options
id: paragraph.available_promotions.field_cm_render_component
field_name: field_cm_render_component
entity_type: paragraph
bundle: available_promotions
label: 'Render Component'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string

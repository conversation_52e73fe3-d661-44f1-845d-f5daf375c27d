uuid: 4010c82e-460b-4796-8c38-caa16f2f14a6
langcode: en
status: true
dependencies:
  config:
    - paragraphs.paragraphs_type.product_configurator
id: paragraph.product_configurator.created
field_name: created
entity_type: paragraph
bundle: product_configurator
label: 'Authored on'
description: 'The time that the Paragraph was created.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: created

uuid: db4a7507-aaa1-4d03-bc68-0e069be46c6a
langcode: de
status: true
dependencies:
  config:
    - field.storage.node.field_cm_brands_hidden
    - node.type.smartphone
    - taxonomy.vocabulary.brands
_core:
  default_config_hash: 2uAjlgdEnzc_RhZOrjkcLXt2YqgYuuKMtL9pp5VqQCU
id: node.smartphone.field_cm_brands_hidden
field_name: field_cm_brands_hidden
entity_type: node
bundle: smartphone
label: 'Hide on brands'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      brands: brands
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference

uuid: 5c1e93be-9c8b-4887-b183-f91e32b06bed
langcode: fr
status: true
dependencies:
  config:
    - node.type.product_option
id: node.product_option.uid
field_name: uid
entity_type: node
bundle: product_option
label: 'Écrit par'
description: "Le nom d'utilisateur de l'auteur du contenu."
required: false
translatable: true
default_value: {  }
default_value_callback: 'Drupal\node\Entity\Node::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference

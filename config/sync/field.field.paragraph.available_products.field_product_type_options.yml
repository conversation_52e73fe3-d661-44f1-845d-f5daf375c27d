uuid: a2f71b0c-57cc-4696-8a99-380478234b4f
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_product_type_options
    - paragraphs.paragraphs_type.available_products
  module:
    - options
id: paragraph.available_products.field_product_type_options
field_name: field_product_type_options
entity_type: paragraph
bundle: available_products
label: 'Product Type (Wireless)'
description: 'Please select the product type to indicate if prepaid or postpaid should be loaded.'
required: false
translatable: false
default_value:
  -
    value: POSTPAID
default_value_callback: ''
settings: {  }
field_type: list_string

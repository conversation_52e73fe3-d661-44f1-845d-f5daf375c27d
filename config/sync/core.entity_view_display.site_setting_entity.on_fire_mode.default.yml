uuid: 3c80d992-b393-429b-9e8a-5e4049839fa9
langcode: fr
status: true
dependencies:
  config:
    - field.field.site_setting_entity.on_fire_mode.field_delay
    - site_settings.site_setting_entity_type.on_fire_mode
  module:
    - user
id: site_setting_entity.on_fire_mode.default
targetEntityType: site_setting_entity
bundle: on_fire_mode
mode: default
content:
  description:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_delay:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 2
    region: content
  group:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -4
    region: content
  user_id:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  langcode: true
  search_api_excerpt: true

uuid: 910e9d86-2b12-4e4f-9d3e-8ddec8de0baf
langcode: de
status: true
dependencies:
  config:
    - field.storage.paragraph.field_display_product_options
    - paragraphs.paragraphs_type.available_products
id: paragraph.available_products.field_display_product_options
field_name: field_display_product_options
entity_type: paragraph
bundle: available_products
label: 'Display Product Options'
description: 'Enable this field to display the options of this product in the teaser card'
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: An
  off_label: Aus
field_type: boolean

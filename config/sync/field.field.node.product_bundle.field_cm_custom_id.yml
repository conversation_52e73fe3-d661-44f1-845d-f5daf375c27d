uuid: aae9bc35-c9c6-44ce-9237-869977097c63
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_cm_custom_id
    - node.type.product_bundle
id: node.product_bundle.field_cm_custom_id
field_name: field_cm_custom_id
entity_type: node
bundle: product_bundle
label: 'Custom ID'
description: 'Provide an ID or custom alias for internal processing. The value of this field can be used for pushing data to state, change current URL on product configurator for example.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string

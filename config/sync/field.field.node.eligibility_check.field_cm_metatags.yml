uuid: 700f0288-cb19-431c-b173-26ac4359d0f4
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_cm_metatags
    - node.type.eligibility_check
  module:
    - metatag
id: node.eligibility_check.field_cm_metatags
field_name: field_cm_metatags
entity_type: node
bundle: eligibility_check
label: 'Meta tags'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: metatag

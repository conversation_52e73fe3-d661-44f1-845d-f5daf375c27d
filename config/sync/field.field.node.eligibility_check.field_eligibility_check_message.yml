uuid: 2f644981-4a14-403e-b193-823f6bdbdf71
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.field_eligibility_check_message
    - node.type.eligibility_check
  module:
    - text
id: node.eligibility_check.field_eligibility_check_message
field_name: field_eligibility_check_message
entity_type: node
bundle: eligibility_check
label: 'Eligibility check error message'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: text_long

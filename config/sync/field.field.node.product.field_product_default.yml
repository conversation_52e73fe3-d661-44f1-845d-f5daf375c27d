uuid: d38d6983-c5bb-4c7c-8dd5-7e3e70627a5d
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_product_default
    - node.type.product
id: node.product.field_product_default
field_name: field_product_default
entity_type: node
bundle: product
label: 'Default Abo'
description: 'Flag current product as default among its category. For Wireless product, this field defines the default product in the roaming tool.'
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: Activé
  off_label: Désactivé
field_type: boolean

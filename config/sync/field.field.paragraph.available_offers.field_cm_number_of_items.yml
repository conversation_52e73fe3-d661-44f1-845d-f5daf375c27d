uuid: 4d624c0d-94d6-4a5f-a68c-52f256492a54
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_cm_number_of_items
    - paragraphs.paragraphs_type.available_offers
id: paragraph.available_offers.field_cm_number_of_items
field_name: field_cm_number_of_items
entity_type: paragraph
bundle: available_offers
label: 'Number Of Items'
description: 'Use this field to limit the number of items to display in the list. Leave the field empty to display all the available items based on your configuration'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: integer

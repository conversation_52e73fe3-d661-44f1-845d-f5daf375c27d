uuid: bc338b50-367f-486a-8830-baa77fb457ef
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_product_details
    - node.type.product
    - paragraphs.paragraphs_type.content_accordion
  module:
    - entity_reference_revisions
id: node.product.field_product_details
field_name: field_product_details
entity_type: node
bundle: product
label: 'Product Details'
description: 'Details displayed in accordion.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      content_accordion: content_accordion
    negate: 0
    target_bundles_drag_drop:
      available_products:
        weight: 55
        enabled: false
      available_promotions:
        weight: 56
        enabled: false
      chatbot:
        weight: 57
        enabled: false
      consent_vendors:
        weight: 58
        enabled: false
      container:
        weight: 59
        enabled: false
      content_accordion:
        weight: 60
        enabled: true
      content_tab:
        weight: 61
        enabled: false
      content_tabs:
        weight: 62
        enabled: false
      content_teaser:
        weight: 63
        enabled: false
      cta:
        weight: 64
        enabled: false
      faqs:
        weight: 65
        enabled: false
      from_library:
        weight: 66
        enabled: false
      help:
        weight: 67
        enabled: false
      help_articles:
        weight: 68
        enabled: false
      horizontal_scroll:
        weight: 69
        enabled: false
      horizontal_scroll_tag:
        weight: 70
        enabled: false
      horizontal_story:
        weight: 71
        enabled: false
      hotline_sales:
        weight: 72
        enabled: false
      iframe:
        weight: 73
        enabled: false
      intro_product:
        weight: 74
        enabled: false
      invoice_explanation:
        weight: 75
        enabled: false
      invoice_explanation_item:
        weight: 76
        enabled: false
      media:
        weight: 77
        enabled: false
      medias:
        weight: 78
        enabled: false
      multimedia_element:
        weight: 79
        enabled: false
      news:
        weight: 80
        enabled: false
      product_feature:
        weight: 81
        enabled: false
      product_option:
        weight: 82
        enabled: false
      product_options:
        weight: 83
        enabled: false
      product_scale:
        weight: 84
        enabled: false
      product_scale_option:
        weight: 85
        enabled: false
      product_tab:
        weight: 86
        enabled: false
      product_tabs:
        weight: 87
        enabled: false
      radio_channels:
        weight: 88
        enabled: false
      reach_out:
        weight: 89
        enabled: false
      related_help_articles:
        weight: 90
        enabled: false
      roaming:
        weight: 91
        enabled: false
      search:
        weight: 92
        enabled: false
      search_form:
        weight: 93
        enabled: false
      smartphones:
        weight: 94
        enabled: false
      spacer:
        weight: 95
        enabled: false
      stage_main:
        weight: 96
        enabled: false
      stage_simple:
        weight: 97
        enabled: false
      story_multimedia:
        weight: 98
        enabled: false
      subscription_detail_item:
        weight: 100
        enabled: false
      subscription_details:
        weight: 99
        enabled: false
      teaser_text_image:
        weight: 101
        enabled: false
      text:
        weight: 102
        enabled: false
      text_image:
        weight: 103
        enabled: false
      title_text_element:
        weight: 104
        enabled: false
      tv_channels:
        weight: 105
        enabled: false
      usp_section:
        weight: 106
        enabled: false
      usp_section_item:
        weight: 107
        enabled: false
      webform:
        weight: 108
        enabled: false
field_type: entity_reference_revisions

uuid: d4f18f5a-f21d-4e78-b072-7075a4d84c34
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.media.field_display_in_fullwidth
    - field.field.paragraph.media.field_one_media
    - paragraphs.paragraphs_type.media
  module:
    - media_library
id: paragraph.media.default
targetEntityType: paragraph
bundle: media
mode: default
content:
  brands_hidden:
    type: options_buttons
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  decoration_pattern:
    type: options_select
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_display_in_fullwidth:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_one_media:
    type: media_library_widget
    weight: 1
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  wimc_cm_id:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  color_pattern: true
  compact: true
  created: true
  title: true
  translation: true
  uid: true

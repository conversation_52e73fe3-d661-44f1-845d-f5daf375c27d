uuid: 170553ee-c4e8-42c0-a979-780ce586a0bb
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_content_categories
    - node.type.product
    - taxonomy.vocabulary.content_categories
id: node.product.field_content_categories
field_name: field_content_categories
entity_type: node
bundle: product
label: 'Content Categories'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      content_categories: content_categories
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference

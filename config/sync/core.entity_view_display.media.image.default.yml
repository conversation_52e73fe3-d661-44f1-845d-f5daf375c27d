uuid: 2eef33eb-7e43-42e9-abe0-b411517ed028
langcode: en
status: true
dependencies:
  config:
    - field.field.media.image.field_cm_brands_hidden
    - field.field.media.image.field_cm_media_cms_name
    - field.field.media.image.field_cm_tags
    - field.field.media.image.field_context
    - field.field.media.image.field_media_description
    - field.field.media.image.field_media_image
    - media.type.image
  module:
    - svg_image
id: media.image.default
targetEntityType: media
bundle: image
mode: default
content:
  field_media_image:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
      svg_attributes:
        width: null
        height: null
      svg_render_as_image: true
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  created: true
  field_cm_brands_hidden: true
  field_cm_media_cms_name: true
  field_cm_tags: true
  field_context: true
  field_media_description: true
  langcode: true
  name: true
  search_api_excerpt: true
  thumbnail: true
  uid: true

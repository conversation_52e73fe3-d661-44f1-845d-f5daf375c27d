<?php

namespace Drupal\advance_importer\Operations;

/**
 * Provides the Advance Importer Add Content.
 */
class AddOperation {

  protected const BUNDLE = ['node'=>'type','taxonomy_term'=>'vid'];

  /**
   * Add function
   * @return Boolean
   * */
  public static function execute($fields,$configuration){
    $entity_type = $configuration['entity_type'];
    $bundle = $configuration['entity_type_bundle'];
    $fields[self::BUNDLE[$entity_type]] = $bundle;
    \Drupal::entityTypeManager()->getStorage($entity_type)->create($fields)->save();
    return true;
  }
}

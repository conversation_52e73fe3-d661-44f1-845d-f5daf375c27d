{% set arrow_background = isNegative ? 'bg-primary-red' : 'bg-primary-white' %}
{% set arrow_img = isNegative ? 'white' : 'red' %}
<div class="hidden flex-none pb-12 min-w-[120px]{% if not arrow_background_hover %} sm:flex{% endif %}">
  <div data-swiper-prev
       class="[&.swiper-button-disabled]:opacity-20 select-none transition-all user flex items-center justify-center rounded-l-lg {{ arrow_background }} w-[60px] h-[40px]{% if arrow_background_hover %} invisible {% endif %}">
    <img src="/{{ active_theme_path() }}/dist/svg/utils/navigation-arrow-{{ arrow_img }}.svg" class="rotate-180"
         alt="prev" aria-hidden="true"/>
  </div>
  <div data-swiper-next
       class="[&.swiper-button-disabled]:opacity-20 select-none transition-all flex items-center justify-center rounded-r-lg {{ arrow_background }} w-[60px] h-[40px] {% if arrow_background_hover %} invisible {% endif %}">
    <img src="/{{ active_theme_path() }}/dist/svg/utils/navigation-arrow-{{ arrow_img }}.svg" alt="prev"
         aria-hidden="true"/>
  </div>
</div>
{% if arrow_background_hover %}
  {#
  Secondaries arrows that are show on hover
  #}
  <div data-swiper-hover-arrow-prev
       class="opacity-20 select-none transition-all user absolute sm:left-14 lg:left-28 inset-y-1/2 z-10	flex items-center justify-center rounded-full bg-grey-medium w-[40px] h-[40px] cursor-pointer invisible sm:visible ">
    <img src="/{{ active_theme_path() }}/dist/svg/utils/navigation-arrow-white.svg" class="rotate-180"
         alt="prev" aria-hidden="true"/>
  </div>
  <div data-swiper-hover-arrow-next
       class="select-none transition-all absolute sm:right-14 lg:right-28 inset-y-1/2 z-10 flex items-center justify-center rounded-full bg-grey-medium w-[40px] h-[40px] cursor-pointer invisible sm:visible ">
    <img src="/{{ active_theme_path() }}/dist/svg/utils/navigation-arrow-white.svg" alt="prev"
         aria-hidden="true"/>
  </div>
{% endif %}

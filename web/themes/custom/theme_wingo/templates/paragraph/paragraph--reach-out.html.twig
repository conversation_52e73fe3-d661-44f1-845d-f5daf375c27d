{% extends "paragraph.html.twig" %}
{% block content %}
	{% import '@theme_wingo/_macros/pictures/picture.twig' as picture %}
	{% import '@theme_wingo/_macros/section-cta/section-cta.twig' as section_cta %}
	<div class="content-grid pt-12" {% if formatted_data.bg_color is not empty %} style="background-color: {{ formatted_data.bg_color.hex }}" {% endif %}>
		<div class="flex flex-col sm:flex-row col-start-2">
			<div class="flex flex-col w-full lg:w-3/5 justify-around">
				<div class="flex flex-col mb-12 col-span-12 font-radikal-bold">
					<div class="flex mb-4">
						<p class="text-16">{{ formatted_data.tagline ? formatted_data.tagline : 'wimc_reach_out'|t }}</p>
					</div>
					<h2>{{ formatted_data.title }}</h2>
				</div>
				<div class="flex">
					<div class="flex flex-wrap gap-10 py-10 w-full sm:w-1/3 md:w-full">
						{% for ctaSection in formatted_data.content %}
							{{ section_cta.data({ title: ctaSection.title, text: ctaSection.text, cta: ctaSection.cta }) }}
						{% endfor %}
					</div>
					<div class="hidden sm:flex md:hidden w-2/3 items-end">
						{{ picture.data({ media: formatted_data.media }) }}
					</div>
				</div>
			</div>
			<div class="hidden sm:flex w-2/5 items-end">
				{{ picture.data({ media: formatted_data.media }) }}
			</div>
		</div>
	</div>
{% endblock content %}

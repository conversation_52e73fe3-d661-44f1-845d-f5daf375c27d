{#
/**
 * @file
 * Theme override for a main view template.
 *
 * Available variables:
 * - attributes: Remaining HTML attributes for the element.
 * - css_name: A CSS-safe version of the view name.
 * - css_class: The user-specified classes names, if any.
 * - header: The optional header.
 * - footer: The optional footer.
 * - rows: The results of the view query, if any.
 * - empty: The content to display if there are no rows.
 * - pager: The optional pager next/prev links to display.
 * - exposed: Exposed widget form/info to display.
 * - feed_icons: Optional feed icons to display.
 * - more: An optional link to the next page of results.
 * - title: Title of the view, only used when displaying in the admin preview.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the view title.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the view title.
 * - attachment_before: An optional attachment view to be displayed before the
 *   view content.
 * - attachment_after: An optional attachment view to be displayed after the
 *   view content.
 * - dom_id: Unique id for every view being printed to give unique class for
 *   JavaScript.
 *
 * @see template_preprocess_views_view()
 */
#}
{{ attach_library('theme_wingo/infinite-scrolling') }}
{% set classes = [
  'view',
  'view-' ~ id|clean_class,
  'view-id-' ~ id,
  'view-display-id-' ~ display_id,
  dom_id ? 'js-view-dom-id-' ~ dom_id,
  'relative',
] %}
 {% set total_results = header|render|striptags %}
<div {{ attributes.addClass(classes) }} {% if search_keys is not empty %} data-view-search-results="{{ { event: 'view_search_results', search_term: search_keys, search_results: total_results ? total_results : 0 }|json_encode(constant('JSON_FORCE_OBJECT') b-or constant('JSON_UNESCAPED_SLASHES') b-or constant('JSON_UNESCAPED_UNICODE')) }}" {% endif %} data-view-search>
  <div class="content-grid">
    <div class="default-grid py-12">
      <div class="col-span-12">
        {{ title_prefix }}
        {% if title %}
          {{ title }}
        {% endif %}
        {{ title_suffix }}
          {% if exposed or header or rows %}
          <div class="default-grid">
              <div class="col-start-1 col-end-5 sm:col-start-3 sm:col-end-11">
                  {% endif %}
        {% if exposed %}
          <div class="view-filters mb-10 flex flex-col items-center justify-center mt-20">
            {{ exposed }}
          </div>
        {% endif %}
        {% if attachment_before %}
          <div class="attachment attachment-before self-start">
            {{ attachment_before }}
          </div>
        {% endif %}
                  {% if header %}
                      <div class="view-header mt-8 mb-12">
                        {% if search_keys %}
                          {% if total_results == 0 %}
                            {{ 'wimc_search_no_result_found'|t }}
                          {% else %}
                            {% trans %}
                              {{ total_results }} result for &lt;&lt;{{ search_keys }}&gt;&gt;
                            {% plural total_results %}
                              {{ total_results }} results for &lt;&lt;{{ search_keys }}&gt;&gt;
                            {% endtrans %}
                          {% endif %}
                        {% endif %}
                      </div>
                  {% endif %}
                  {% if rows %}
                      <div data-search-results-rows>
                          {{ rows }}
                      </div>
                  {% elseif empty %}
                      <div class="view-empty mb-24 text-center">
                          {{ empty }}
                      </div>
                  {% endif %}
                  {% if exposed or header or rows %}
              </div>
          </div>
          {% endif %}
        {% if pager %}
          {{ pager }}
        {% endif %}
        {% if attachment_after %}
          <div class="attachment attachment-after">
            {{ attachment_after }}
          </div>
        {% endif %}
        {% if more %}
          {{ more }}
        {% endif %}
        {% if footer %}
          <div class="view-footer">
            {{ footer }}
          </div>
        {% endif %}
        {% if feed_icons %}
          <div class="feed-icons">
            {{ feed_icons }}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
<style>
  .view-filters input[type="submit"] {
    display: none;
  }
</style>

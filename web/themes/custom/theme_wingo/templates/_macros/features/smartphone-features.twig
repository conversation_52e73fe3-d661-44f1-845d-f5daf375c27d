{% macro data(attr = {features: []}) %}
  {% import '@theme_wingo/_macros/section-title/section-title.twig' as section_title %}
  <div class="sm:mx-auto flex flex-col md:px-20 my-16">
    {% set technical_details = 'wimc_smartphones_technical_details'|t %}
    {{ section_title.data({ title: technical_details }) }}
    <div class="content-grid flex justify-center items-center">
      <div class="default-grid -mx-6 sm:mx-0 md:px-12 w-full">
        <div class="col-span-12 rounded-3xl">
          <dl class="justify-center">
            {% set odd = 0 %}
            {% for smartphone_feature in  attr.features %}
              {% if smartphone_feature.feature_values is not empty %}
                {% set odd = odd + 1 %}
                <div class="flex sm:flex-row flex-col w-full !border-t-0 !border-b-0 rounded-2xl px-4	sm:px-0 {%- if odd is even %} bg-grey-lighter {%- endif %}">
                  <h4 class="text-16 md:text-18 font-radikal-bold sm:w-1/2 w-full sm:p-8 pt-3 sm:pt-8">{{ smartphone_feature.id|t }}</h4>
                  {% set feature_value = smartphone_feature.feature_values|safe_join(', ') %}
                  <p class="text-16 md:text-18 text-primary-black sm:w-1/2 w-full sm:p-8 pb-3 sm:pb-8">{{ feature_value }}</p>
                </div>
              {% endif %}
            {% endfor %}
          </dl>
        </div>
      </div>
    </div>
  </div>
{% endmacro %}

{% macro data(attr = {region: [], features: []}) %}
  <div class="rounded-4xl bg-grey-lighter flex flex-col items-center py-8 last:pb-0 h-full" data-subscription-detail-card>
    <div class="flex flex-col items-center{% if not attr.render == "small" %} pb-8{% endif %}">
      <img src="{{ attr.region.icon_path }}" alt="{{ attr.region.name }}" aria-hidden="true"
           class="h-8 mb-2"/>
      <h3 class="{% if attr.render == "small" %}text-14 font-radikal-bold mb-2{% else %}text-h5{% endif %} text-center">{{ attr.region.name }}</h3>
    </div>
    {% for feature_data in attr.features %}
      <div class="flex flex-col {% if attr.render == "small" %}py-3{% else %}py-8{% endif %} px-4 border-b border-b-grey-light last:border-0 w-full items-center">
        <div class="flex">
          {#  <img src="{{ feature_data.feature.icon_path }}" alt="{{ feature_data.feature.title }}" aria-hidden="true"
               class="h-2 mr-2"/> #}
          <div class="{% if attr.render == "small" %}mb-2 text-14{% else %}mb-4 text-h6{% endif %}">{{ feature_data.feature.title }}</div>
        </div>
        <div class="flex flex-col items-center text-center {% if attr.render == "small" %}font-radikal-bold{% else %}mb-4 text-grey-dark{% endif %}">{{ feature_data.details_item.text|raw }}</div>
      </div>
    {% endfor %}
  </div>
{% endmacro %}

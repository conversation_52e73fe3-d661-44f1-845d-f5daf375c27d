{% macro data(attr = { label: '', icons: [], class: '', iconsClass: '' }) %}
  <button class="{{ attr.class }} group inline-flex w-full rounded bg-transparent px-4 py-3 hover:md:bg-grey-light [&.active]:md:bg-grey-light [&.forced-active]:bg-grey-light select-none cursor-pointer font-radikal-bold motion-reduce:transition-none transition-all ease-in-out-expo duration-150">
    {% if attr.icons|length %}
      <div class="{{ attr.iconsClass }} flex relative w-6 mr-2 pointer-events-none">
        {% if attr.icons|length == 1 %}
          {% for icon in attr.icons %}
          <div class="[&>svg *]:stroke-current absolute">
            {{ icon|raw }}
          </div>
          {% endfor %}
          {% else %}
            {% for icon in attr.icons %}
              <div class="[&>svg *]:stroke-current absolute motion-reduce:transition-none transition-all ease-in-out-expo duration-150 {{ loop.index == 1 ? 'opacity-100 group-hover:md:opacity-0' : 'opacity-0 group-hover:md:opacity-100' }}">
                {{ icon|raw }}
              </div>
            {% endfor %}
        {% endif %}
      </div>
    {% endif %}
    {% if attr.label %}
      {{ attr.label }}
    {% endif %}
  </button>
{% endmacro %}

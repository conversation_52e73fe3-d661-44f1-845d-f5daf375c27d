{% macro render(attr = {desktopUrl: '', mobileUrl: '', classes: []}) %}
  {% set desktopUrl = attr.desktopUrl %}
  {% set mobileUrl = attr.mobileUrl %}
  {% if desktopUrl is iterable %}
    {% set desktopUrl = '' %}
  {% endif %}
  {% if mobileUrl is iterable %}
    {% set mobileUrl = '' %}
  {% endif %}
  {% if desktopUrl or mobileUrl %}
    {{ attach_library('theme_wingo/video-lazy-load') }}
    {% if desktopUrl and mobileUrl %}
      {{ attach_library('theme_wingo/responsive-video') }}
      <video data-video-element data-lazy data-desktop-video="{{ desktopUrl }}" data-mobile-video="{{ mobileUrl }}" {% if attr.classes is not empty -%} class="{{ attr.classes|join(' ') }}" {%- endif %}type="video/mp4" playsinline autoplay muted loop></video>
    {% elseif desktopUrl %}
      {% set videoClasses = ['hidden', 'sm:flex'] %}
      {% if attr.classes %}
        {% set videoClasses = videoClasses|merge(attr.classes) %}
      {% endif %}
      <video data-lazy data-src="{{ desktopUrl }}" class="{{ videoClasses|join(' ') }}" type="video/mp4" playsinline autoplay muted loop></video>
    {% elseif mobileUrl %}
      {% set videoClasses = ['sm:hidden', 'flex'] %}
      {% if attr.classes %}
        {% set videoClasses = videoClasses|merge(attr.classes) %}
      {% endif %}
      <video data-lazy data-src="{{ mobileUrl }}" class="{{ videoClasses|join(' ') }}" type="video/mp4" playsinline autoplay muted loop></video>
    {% endif %}
  {% endif %}
{% endmacro %}

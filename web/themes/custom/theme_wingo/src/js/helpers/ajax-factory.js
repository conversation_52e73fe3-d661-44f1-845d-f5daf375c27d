export default class AjaxFactory {
  constructor({url, method, params, statusKey, successKey, successCallback, errorCallback, target, spinnerTarget}) {
    this.method = method || 'GET'
    this.url = url;
    this.params = params;
    this.statusKey = statusKey;
    this.successKey = successKey;
    this.successCallback = successCallback;
    this.errorCallback = errorCallback;
    this.target = target;
    this.spinnerTarget = spinnerTarget;
    /* Class members */
    this.response = '';
    this.status = '';
    this.jsonData = null;
  }

  getResponseStatus() {
    return this.statusKey.split('.').reduce((prev, cur) => prev[cur], this.jsonData)
  }
  dispatchCallback() {
    if (this.response.status === 200) {
      this.successCallback(this.jsonData);
    } else {
      this.errorCallback(this.jsonData);
    }
  }
  async call(){
    const $this = this;
    try {
      let initRequestConfig = {
        method: $this.method,
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        },
      }
      // Prepare request body
      if (this.params && this.method === 'POST') {
        initRequestConfig.body = JSON.stringify(this.params);
      } // Call should provide full url with the query params
      fetch(this.url, initRequestConfig)
        .then((response) => {
          $this.response = response;
        if (response.ok) { // HTTP OK
          return response.json();
        } else {
          return Promise.reject(response);
        }
      }).then((data) => {
        // Get the JSON of the response
        $this.jsonData = data;
        $this.dispatchCallback();
      }).catch((err) => {
        $this.errorCallback(err);
      });
    }
    catch {
      this.errorCallback(this.response);
    }
  }
}
